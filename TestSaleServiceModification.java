/**
 * 测试 SaleServiceImpl.getInvoicedAndDeliveryList 方法的修改
 * 
 * 修改说明：
 * 1. 原方法使用单一SQL查询
 * 2. 修改后根据 divisionWay 字段分两次查询并合并结果：
 *    - divisionWay 为空或1：销售决定，使用原有查询逻辑
 *    - divisionWay 为2：财务决定，使用新的查询逻辑
 * 3. 两次查询结果通过 mergeQueryResults 方法合并
 * 
 * 主要变化：
 * - 添加了获取 SlInvoiceApplication 对象的逻辑来判断 divisionWay
 * - 将原有的单一查询拆分为两个查询（sql1 和 sql2）
 * - sql1：处理 divisionWay 为空或1的情况
 * - sql2：处理 divisionWay 为2的情况，使用 t_sl_invoice_application_detail 表
 * - 添加了 mergeQueryResults 私有方法来合并两个查询结果
 * 
 * 参考实现：
 * getApplicationItemListByApplicationId 方法的实现模式
 */
public class TestSaleServiceModification {
    
    public static void main(String[] args) {
        System.out.println("SaleServiceImpl.getInvoicedAndDeliveryList 方法修改完成");
        System.out.println("修改内容：");
        System.out.println("1. 根据 divisionWay 分两次查询");
        System.out.println("2. 合并查询结果");
        System.out.println("3. 参考 getApplicationItemListByApplicationId 的实现模式");
    }
}
